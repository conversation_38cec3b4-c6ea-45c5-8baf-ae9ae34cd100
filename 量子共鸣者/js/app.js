/**
 * 量子共鸣者 - 主应用程序
 * 应用程序入口点，负责初始化和协调各个模块
 */

class QuantumResonanceApp {
    constructor() {
        this.isInitialized = false;
        this.isLoading = true;
        this.loadingProgress = 0;
        
        // 应用状态
        this.appState = 'loading'; // 'loading', 'menu', 'game', 'settings'
        
        // 版本信息
        this.version = '1.0.0';
        this.buildDate = '2024-07-30';
        
        console.log('🚀 量子共鸣者应用程序启动');
        console.log(`📦 版本: ${this.version} (${this.buildDate})`);
    }

    /**
     * 初始化应用程序
     */
    async init() {
        try {
            console.log('🚀 开始初始化应用程序...');
            
            // 显示加载界面
            this.showLoadingScreen();
            
            // 初始化步骤
            await this.initializeModules();
            await this.setupEventListeners();
            await this.loadDefaultLevels();
            await this.setupUI();
            
            // 完成初始化
            this.isInitialized = true;
            this.isLoading = false;
            this.appState = 'menu';
            
            // 隐藏加载界面，显示主菜单
            await this.hideLoadingScreen();
            this.showMainMenu();
            
            console.log('✅ 应用程序初始化完成');
            
        } catch (error) {
            console.error('❌ 应用程序初始化失败:', error);
            this.showError('应用程序初始化失败', error.message);
        }
    }

    /**
     * 初始化各个模块
     */
    async initializeModules() {
        const steps = [
            { name: '存储服务', fn: () => this.initStorageService() },
            { name: '国际化服务', fn: () => this.initI18nService() },
            { name: 'UI管理器', fn: () => this.initUIManager() },
            { name: 'UI动画系统', fn: () => this.initUIAnimations() },
            { name: '关卡选择界面', fn: () => this.initLevelSelect() },
            { name: '关卡管理器', fn: () => this.initLevelManager() },
            { name: '关卡编辑器', fn: () => this.initLevelEditor() },
            { name: '游戏结束屏幕', fn: () => this.initGameOver() },
            { name: '玩家系统管理器', fn: () => this.initPlayerManager() },
            { name: '成就系统UI', fn: () => this.initAchievementsUI() },
            { name: '排行榜UI', fn: () => this.initLeaderboardUI() },
            { name: '渲染引擎', fn: () => this.initRenderEngine() },
            { name: '音频管理器', fn: () => this.initAudioManager() },
            { name: '游戏控制器', fn: () => this.initGameController() },
        ];

        for (let i = 0; i < steps.length; i++) {
            const step = steps[i];
            console.log(`📦 初始化 ${step.name}...`);

            try {
                await step.fn();
                this.updateLoadingProgress((i + 1) / steps.length * 0.6); // 60% for modules
                console.log(`✅ ${step.name} 初始化完成`);
            } catch (error) {
                console.error(`❌ ${step.name} 初始化失败:`, error);
                // 对于非关键模块，记录错误但继续初始化
                if (this.isCriticalModule(step.name)) {
                    throw new Error(`${step.name}初始化失败: ${error.message}`);
                } else {
                    console.warn(`⚠️ ${step.name}初始化失败，但继续其他模块初始化: ${error.message}`);
                }
            }
        }
    }

    /**
     * 检查是否为关键模块
     * @param {string} moduleName - 模块名称
     * @returns {boolean} 是否为关键模块
     */
    isCriticalModule(moduleName) {
        const criticalModules = ['存储服务', '国际化服务', 'UI管理器', '游戏控制器'];
        return criticalModules.includes(moduleName);
    }

    /**
     * 初始化存储服务
     */
    async initStorageService() {
        if (window.storageService) {
            await storageService.init();
        } else {
            console.warn('⚠️ 存储服务未找到，跳过初始化');
        }
    }

    /**
     * 初始化国际化服务
     */
    async initI18nService() {
        if (window.i18n) {
            await i18n.init();
        } else {
            console.warn('⚠️ 国际化服务未找到，跳过初始化');
        }
    }

    /**
     * 初始化UI管理器
     */
    async initUIManager() {
        if (window.uiManager) {
            await uiManager.init();
        } else {
            console.warn('⚠️ UI管理器未找到，跳过初始化');
        }
    }

    /**
     * 初始化UI动画系统
     */
    async initUIAnimations() {
        if (window.uiAnimations) {
            await uiAnimations.init();
        } else {
            console.warn('⚠️ UI动画系统未找到，跳过初始化');
        }
    }

    /**
     * 初始化关卡选择界面
     */
    async initLevelSelect() {
        if (window.levelSelect) {
            await levelSelect.init();
        } else {
            console.warn('⚠️ 关卡选择界面未找到，跳过初始化');
        }
    }

    /**
     * 初始化关卡管理器
     */
    async initLevelManager() {
        if (window.levelManager) {
            await levelManager.init();
        } else {
            console.warn('⚠️ 关卡管理器未找到，跳过初始化');
        }
    }

    /**
     * 初始化关卡编辑器
     */
    async initLevelEditor() {
        if (window.levelEditor) {
            await levelEditor.init();
        } else {
            console.warn('⚠️ 关卡编辑器未找到，跳过初始化');
        }
    }

    /**
     * 初始化游戏结束屏幕
     */
    async initGameOver() {
        if (window.gameOver) {
            await gameOver.init();
        } else {
            console.warn('⚠️ 游戏结束屏幕未找到，跳过初始化');
        }
    }

    /**
     * 初始化玩家系统管理器
     */
    async initPlayerManager() {
        if (window.playerManager) {
            await playerManager.init();
        } else {
            console.warn('⚠️ 玩家系统管理器未找到，跳过初始化');
        }
    }

    /**
     * 初始化成就系统UI
     */
    async initAchievementsUI() {
        if (window.achievementsUI) {
            await achievementsUI.init();
        } else {
            console.warn('⚠️ 成就系统UI未找到，跳过初始化');
        }
    }

    /**
     * 初始化排行榜UI
     */
    async initLeaderboardUI() {
        if (window.leaderboardUI) {
            await leaderboardUI.init();
        } else {
            console.warn('⚠️ 排行榜UI未找到，跳过初始化');
        }
    }

    /**
     * 初始化音频管理器
     */
    async initAudioManager() {
        if (window.audioManager) {
            await audioManager.init();
        } else {
            console.warn('⚠️ 音频管理器未找到，跳过初始化');
        }
    }

    /**
     * 初始化游戏控制器
     */
    async initGameController() {
        if (window.gameController) {
            console.log('🎮 开始初始化游戏控制器...');
            await gameController.init();
            console.log('🎮 游戏控制器初始化完成，状态:', gameController.isInitialized);
        } else {
            throw new Error('游戏控制器未找到，这是关键模块，无法继续');
        }
    }

    /**
     * 初始化渲染引擎
     */
    async initRenderEngine() {
        const canvas = document.getElementById('game-canvas') || document.getElementById('gameCanvas');
        if (!canvas) {
            console.warn('⚠️ 游戏画布元素未找到，跳过渲染引擎初始化');
            return;
        }

        if (window.renderEngine) {
            // 初始化渲染引擎
            const success = await renderEngine.init(canvas);
            if (!success) {
                console.warn('⚠️ 渲染引擎初始化失败');
                return;
            }

            // 设置音频可视化器画布
            this.setupAudioVisualizer();

            console.log(`🎨 渲染引擎初始化完成 (模式: ${renderEngine.getRenderMode()})`);
        } else {
            console.warn('⚠️ 渲染引擎未找到，跳过初始化');
        }
    }

    /**
     * 设置音频可视化器
     */
    setupAudioVisualizer() {
        const visualizerCanvas = document.getElementById('audioVisualizerCanvas');
        if (visualizerCanvas && audioManager && audioManager.visualizer) {
            audioManager.setupVisualizer(visualizerCanvas);
            console.log('🎵 音频可视化器已设置');
        }
    }

    /**
     * 设置事件监听器
     */
    async setupEventListeners() {
        console.log('🔗 设置事件监听器...');
        
        // 窗口事件
        window.addEventListener('beforeunload', () => this.onBeforeUnload());
        window.addEventListener('resize', () => this.onWindowResize());
        window.addEventListener('visibilitychange', () => this.onVisibilityChange());
        
        // 错误处理
        window.addEventListener('error', (e) => this.onError(e));
        window.addEventListener('unhandledrejection', (e) => this.onUnhandledRejection(e));
        
        // 菜单按钮事件
        this.setupMenuEvents();
        
        this.updateLoadingProgress(0.8); // 80%
    }

    /**
     * 设置菜单事件
     */
    setupMenuEvents() {
        // 开始游戏按钮
        const startButton = document.getElementById('startGameButton');
        if (startButton) {
            startButton.addEventListener('click', () => this.startGame());
        } else {
            console.warn('⚠️ 开始游戏按钮不存在');
        }

        // 设置按钮
        const settingsButton = document.getElementById('settingsButton');
        if (settingsButton) {
            settingsButton.addEventListener('click', () => this.showSettings());
        } else {
            console.warn('⚠️ 设置按钮不存在');
        }

        // 关于按钮
        const aboutButton = document.getElementById('aboutButton');
        if (aboutButton) {
            aboutButton.addEventListener('click', () => this.showAbout());
        } else {
            console.warn('⚠️ 关于按钮不存在');
        }
        
        // 返回主菜单按钮
        const backButtons = document.querySelectorAll('.back-to-menu');
        backButtons.forEach(button => {
            button.addEventListener('click', () => this.showMainMenu());
        });
        
        // 暂停菜单按钮
        const pauseButton = document.getElementById('pauseButton');
        if (pauseButton) {
            pauseButton.addEventListener('click', () => gameController.pauseGame());
        }
        
        // 恢复游戏按钮
        const resumeButton = document.getElementById('resumeButton');
        if (resumeButton) {
            resumeButton.addEventListener('click', () => gameController.resumeGame());
        }
        
        // 重启游戏按钮
        const restartButton = document.getElementById('restartButton');
        if (restartButton) {
            restartButton.addEventListener('click', () => gameController.restartGame());
        }
        
        // 音频初始化按钮（需要用户交互）
        const initAudioButton = document.getElementById('initAudioButton');
        if (initAudioButton) {
            initAudioButton.addEventListener('click', async () => {
                await this.initializeAudio();
                initAudioButton.style.display = 'none';
            });
        }
    }

    /**
     * 加载默认关卡
     */
    async loadDefaultLevels() {
        console.log('📋 加载默认关卡...');
        
        // 注册基础关卡包
        levelManager.registerLevelPack('basic', {
            name: '基础教程',
            description: '学习量子共鸣的基本机制',
            levels: [
                {
                    name: '第一步',
                    description: '点击粒子来激活它们',
                    particles: [
                        { x: 300, y: 200, frequency: 440, radius: 15 },
                        { x: 500, y: 200, frequency: 880, radius: 15 }
                    ],
                    targetScore: 500,
                    timeLimit: 30,
                    objectives: [
                        { type: 'activateParticles', target: 2, description: '激活所有粒子' }
                    ]
                },
                {
                    name: '共鸣链',
                    description: '创建粒子之间的共鸣连锁反应',
                    particles: [
                        { x: 200, y: 200, frequency: 440, radius: 12 },
                        { x: 350, y: 200, frequency: 440, radius: 12 },
                        { x: 500, y: 200, frequency: 440, radius: 12 },
                        { x: 275, y: 300, frequency: 660, radius: 12 },
                        { x: 425, y: 300, frequency: 660, radius: 12 }
                    ],
                    targetScore: 1000,
                    timeLimit: 45,
                    objectives: [
                        { type: 'chainReaction', target: 3, description: '创建3个粒子的连锁反应' }
                    ]
                },
                {
                    name: '频率调节',
                    description: '学习调节频率来匹配不同的粒子',
                    particles: [
                        { x: 200, y: 150, frequency: 220, radius: 10 },
                        { x: 400, y: 150, frequency: 440, radius: 10 },
                        { x: 600, y: 150, frequency: 880, radius: 10 },
                        { x: 300, y: 300, frequency: 330, radius: 10 },
                        { x: 500, y: 300, frequency: 660, radius: 10 }
                    ],
                    targetScore: 1500,
                    timeLimit: 60,
                    rules: {
                        frequencyRange: { min: 200, max: 1000 }
                    }
                }
            ]
        });
        
        // 注册进阶关卡包
        levelManager.registerLevelPack('advanced', {
            name: '进阶挑战',
            description: '更复杂的量子共鸣挑战',
            levels: [
                {
                    name: '量子迷宫',
                    description: '在复杂的粒子网络中找到最佳路径',
                    particles: this.generateMazeParticles(),
                    targetScore: 2500,
                    timeLimit: 90,
                    maxMoves: 20
                },
                {
                    name: '共鸣风暴',
                    description: '在动态变化的环境中保持共鸣',
                    particles: this.generateStormParticles(),
                    forceFields: [
                        { x: 400, y: 300, type: 'vortex', strength: 150, radius: 200 }
                    ],
                    targetScore: 3000,
                    timeLimit: 120,
                    rules: {
                        damping: 0.95,
                        fieldStrength: 1.5
                    }
                }
            ]
        });
        
        this.updateLoadingProgress(0.9); // 90%
    }

    /**
     * 生成迷宫粒子配置
     */
    generateMazeParticles() {
        const particles = [];
        const gridSize = 8;
        const cellSize = 80;
        const baseFreq = 220;
        
        for (let x = 0; x < gridSize; x++) {
            for (let y = 0; y < gridSize; y++) {
                // 跳过一些位置创建迷宫效果
                if ((x + y) % 3 === 0) continue;
                
                particles.push({
                    x: 100 + x * cellSize,
                    y: 100 + y * cellSize,
                    frequency: baseFreq + (x + y) * 30,
                    radius: 8,
                    isTarget: x === gridSize - 1 && y === gridSize - 1
                });
            }
        }
        
        return particles;
    }

    /**
     * 生成风暴粒子配置
     */
    generateStormParticles() {
        const particles = [];
        const centerX = 400;
        const centerY = 300;
        const radius = 200;
        const count = 12;
        
        for (let i = 0; i < count; i++) {
            const angle = (i / count) * Math.PI * 2;
            const x = centerX + Math.cos(angle) * radius;
            const y = centerY + Math.sin(angle) * radius;
            
            particles.push({
                x: x,
                y: y,
                frequency: 440 + i * 55,
                radius: 10,
                energy: 1 + Math.random()
            });
        }
        
        return particles;
    }

    /**
     * 设置UI
     */
    async setupUI() {
        console.log('🎨 设置用户界面...');
        
        // 更新版本信息
        const versionElement = document.getElementById('versionInfo');
        if (versionElement) {
            versionElement.textContent = `v${this.version}`;
        }
        
        // 设置语言选择器
        const languageSelect = document.getElementById('languageSelect');
        if (languageSelect) {
            languageSelect.addEventListener('change', (e) => {
                i18n.setLanguage(e.target.value);
            });
        }
        
        // 初始化设置面板
        this.initializeSettingsPanel();

        // 设置音频可视化器
        this.setupAudioVisualizer();

        this.updateLoadingProgress(1.0); // 100%
    }

    /**
     * 设置音频可视化器
     */
    setupAudioVisualizer() {
        const visualizerCanvas = document.getElementById('audioVisualizerCanvas');
        if (visualizerCanvas && audioManager) {
            audioManager.setupVisualizer(visualizerCanvas);
            console.log('📊 音频可视化器已设置');
        }
    }

    /**
     * 初始化设置面板
     */
    initializeSettingsPanel() {
        // 音量控制
        const volumeSliders = {
            master: document.getElementById('masterVolumeSlider'),
            music: document.getElementById('musicVolumeSlider'),
            sfx: document.getElementById('sfxVolumeSlider')
        };
        
        Object.entries(volumeSliders).forEach(([type, slider]) => {
            if (slider) {
                slider.addEventListener('input', (e) => {
                    const volume = parseFloat(e.target.value);
                    if (audioEngine.isReady()) {
                        audioEngine.setVolume(type, volume);
                    }
                    gameController.settings[`${type}Volume`] = volume;
                    gameController.saveSettings();
                });
            }
        });
        
        // 图形设置
        const particleEffectsToggle = document.getElementById('particleEffectsToggle');
        if (particleEffectsToggle) {
            particleEffectsToggle.addEventListener('change', (e) => {
                gameController.settings.enableParticleEffects = e.target.checked;
                renderEngine.enableParticleTrails = e.target.checked;
                renderEngine.enableGlowEffects = e.target.checked;
                gameController.saveSettings();
            });
        }
        
        // 调试模式
        const debugToggle = document.getElementById('debugToggle');
        if (debugToggle) {
            debugToggle.addEventListener('change', (e) => {
                renderEngine.showDebugInfo = e.target.checked;
            });
        }
    }

    /**
     * 初始化音频（需要用户交互）
     */
    async initializeAudio() {
        try {
            console.log('🎵 初始化音频系统...');

            // 初始化音频引擎
            await audioEngine.init();
            console.log('✅ 音频引擎初始化完成');

            // 初始化音频管理器
            if (window.audioManager && !audioManager.isInitialized) {
                await audioManager.init();
                console.log('✅ 音频管理器初始化完成');
            }

            console.log('✅ 音频系统初始化完成');
        } catch (error) {
            console.error('❌ 音频系统初始化失败:', error);
        }
    }

    /**
     * 开始游戏
     */
    async startGame() {
        try {
            this.appState = 'game';

            // 确保音频已初始化
            if (!audioEngine.isReady()) {
                await this.initializeAudio();
            }

            // 确保音频管理器已初始化
            if (window.audioManager && !audioManager.isInitialized) {
                await audioManager.init();
            }

            // 开始第一个关卡
            await gameController.startGame();

        } catch (error) {
            console.error('❌ 游戏启动失败:', error);
            this.showError('游戏启动失败', error.message);
        }
    }

    /**
     * 显示主菜单
     */
    showMainMenu() {
        this.appState = 'menu';
        console.log('🏠 显示主菜单');

        // 使用UI管理器的屏幕切换功能
        if (window.uiManager && uiManager.showScreen) {
            uiManager.showScreen('main-menu-screen');
            console.log('✅ 主菜单已显示（UI管理器）');
        } else {
            // 备用方案：直接操作DOM
            console.log('🔧 使用备用方案显示主菜单');
            this.hideAllScreens();

            const mainMenu = document.getElementById('main-menu-screen');
            if (mainMenu) {
                mainMenu.classList.add('active');
                console.log('✅ 主菜单已显示（备用方案）');
            } else {
                console.error('❌ 主菜单元素不存在: main-menu-screen');
            }
        }
    }

    /**
     * 显示设置界面
     */
    showSettings() {
        this.appState = 'settings';
        this.hideAllScreens();
        const settingsScreen = document.getElementById('settingsScreen');
        if (settingsScreen) {
            settingsScreen.style.display = 'block';
        } else {
            console.warn('⚠️ 设置界面元素不存在');
        }
    }

    /**
     * 显示关于界面
     */
    showAbout() {
        this.hideAllScreens();
        const aboutScreen = document.getElementById('aboutScreen');
        if (aboutScreen) {
            aboutScreen.style.display = 'block';
        } else {
            console.warn('⚠️ 关于界面元素不存在');
        }
    }

    /**
     * 显示加载界面
     */
    showLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.classList.add('active');
            loadingScreen.style.display = 'flex';
            loadingScreen.style.visibility = 'visible';
            loadingScreen.style.opacity = '1';
            console.log('📱 显示加载界面');
        } else {
            console.warn('⚠️ 加载界面元素不存在: loading-screen');
        }
    }

    /**
     * 隐藏加载界面
     */
    async hideLoadingScreen() {
        return new Promise((resolve) => {
            const loadingScreen = document.getElementById('loading-screen');
            if (loadingScreen) {
                loadingScreen.classList.remove('active');
                loadingScreen.style.visibility = 'hidden';
                loadingScreen.style.opacity = '0';

                // 延迟隐藏display属性，让动画完成
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                    console.log('📱 加载界面已隐藏');
                    resolve();
                }, 300);
            } else {
                console.warn('⚠️ 加载界面元素不存在: loading-screen');
                resolve();
            }
        });
    }

    /**
     * 隐藏所有界面
     */
    hideAllScreens() {
        const screens = [
            'loading-screen', 'main-menu-screen', 'game-screen',
            'settings-screen', 'pause-screen', 'player-screen',
            'levelSelectScreen', 'levelEditorScreen', 'gameOverScreen',
            'achievementsScreen', 'leaderboardScreen'
        ];

        screens.forEach(screenId => {
            const screen = document.getElementById(screenId);
            if (screen) {
                screen.classList.remove('active');
                // 移除内联样式，让CSS类控制显示状态
                screen.style.removeProperty('visibility');
                screen.style.removeProperty('opacity');
                screen.style.removeProperty('display');
            }
        });
    }

    /**
     * 更新加载进度
     * @param {number} progress - 进度值 (0-1)
     */
    updateLoadingProgress(progress) {
        this.loadingProgress = progress;

        const progressFill = document.getElementById('loading-progress-fill');
        if (progressFill) {
            progressFill.style.width = `${progress * 100}%`;
        } else {
            console.warn('⚠️ 进度条元素不存在: loading-progress-fill');
        }

        const progressText = document.getElementById('loading-text');
        if (progressText) {
            const percentage = Math.round(progress * 100);
            if (percentage < 20) {
                progressText.textContent = '正在初始化量子场...';
            } else if (percentage < 40) {
                progressText.textContent = '正在加载音频引擎...';
            } else if (percentage < 60) {
                progressText.textContent = '正在初始化渲染系统...';
            } else if (percentage < 80) {
                progressText.textContent = '正在设置用户界面...';
            } else if (percentage < 100) {
                progressText.textContent = '正在完成最后的准备...';
            } else {
                progressText.textContent = '初始化完成！';
            }
        } else {
            console.warn('⚠️ 进度文本元素不存在: loading-text');
        }

        console.log(`📊 加载进度: ${Math.round(progress * 100)}%`);
    }

    /**
     * 显示错误信息
     * @param {string} title - 错误标题
     * @param {string} message - 错误消息
     */
    showError(title, message) {
        console.error(`❌ ${title}: ${message}`);
        
        // 简单的错误显示，后续可以改为更好的UI
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.innerHTML = `
            <h3>${title}</h3>
            <p>${message}</p>
            <button onclick="this.parentElement.remove()">确定</button>
        `;
        
        document.body.appendChild(errorDiv);
    }

    // 事件处理器
    onBeforeUnload() {
        // 保存游戏状态
        if (gameController.isInitialized) {
            gameController.saveSettings();
        }
    }

    onWindowResize() {
        if (renderEngine.canvas) {
            renderEngine.resize();
        }
    }

    onVisibilityChange() {
        if (document.hidden && gameController.gameState === 'playing') {
            gameController.pauseGame();
        }
    }

    onError(event) {
        console.error('❌ 全局错误:', event.error);
        this.showError('应用程序错误', event.error.message);
    }

    onUnhandledRejection(event) {
        console.error('❌ 未处理的Promise拒绝:', event.reason);
        this.showError('异步操作失败', event.reason.message || event.reason);
    }
}

// 导出应用程序类到全局作用域
window.QuantumResonanceApp = QuantumResonanceApp;

// 注意：应用程序的启动代码在HTML文件的内联脚本中
