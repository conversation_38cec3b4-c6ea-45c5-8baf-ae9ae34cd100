/**
 * 屏幕显示修复脚本
 * 用于调试和修复主菜单显示问题
 */

(function() {
    'use strict';
    
    console.log('🔧 启动屏幕显示修复脚本...');
    
    // 等待DOM加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initScreenFix);
    } else {
        initScreenFix();
    }
    
    function initScreenFix() {
        console.log('🔍 检查屏幕状态...');
        
        // 检查所有屏幕元素
        const screens = {
            'loading-screen': document.getElementById('loading-screen'),
            'main-menu-screen': document.getElementById('main-menu-screen'),
            'game-screen': document.getElementById('game-screen'),
            'settings-screen': document.getElementById('settings-screen'),
            'pause-screen': document.getElementById('pause-screen')
        };
        
        // 输出屏幕状态
        Object.entries(screens).forEach(([id, element]) => {
            if (element) {
                const styles = window.getComputedStyle(element);
                console.log(`📱 屏幕 ${id}:`, {
                    exists: true,
                    display: styles.display,
                    visibility: styles.visibility,
                    opacity: styles.opacity,
                    zIndex: styles.zIndex,
                    hasActiveClass: element.classList.contains('active')
                });
            } else {
                console.log(`❌ 屏幕 ${id}: 不存在`);
            }
        });
        
        // 强制显示主菜单的修复函数
        window.forceShowMainMenu = function() {
            console.log('🔧 强制显示主菜单...');
            
            const loadingScreen = document.getElementById('loading-screen');
            const mainMenuScreen = document.getElementById('main-menu-screen');
            
            // 隐藏加载屏幕
            if (loadingScreen) {
                loadingScreen.classList.remove('active');
                loadingScreen.style.display = 'none';
                loadingScreen.style.visibility = 'hidden';
                loadingScreen.style.opacity = '0';
                console.log('✅ 加载屏幕已隐藏');
            }
            
            // 显示主菜单
            if (mainMenuScreen) {
                mainMenuScreen.classList.add('active');
                mainMenuScreen.style.display = 'flex';
                mainMenuScreen.style.visibility = 'visible';
                mainMenuScreen.style.opacity = '1';
                mainMenuScreen.style.zIndex = '10';
                console.log('✅ 主菜单已显示');
                
                // 初始化背景画布
                initMenuBackground();
                
                return true;
            } else {
                console.error('❌ 主菜单屏幕元素不存在');
                return false;
            }
        };
        
        // 初始化菜单背景
        function initMenuBackground() {
            const canvas = document.getElementById('menu-background-canvas');
            if (!canvas) {
                console.warn('⚠️ 菜单背景画布未找到');
                return;
            }
            
            const ctx = canvas.getContext('2d');
            
            // 设置画布大小
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
            
            // 简单的粒子动画
            const particles = [];
            const particleCount = 30;
            
            // 创建粒子
            for (let i = 0; i < particleCount; i++) {
                particles.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    vx: (Math.random() - 0.5) * 0.3,
                    vy: (Math.random() - 0.5) * 0.3,
                    size: Math.random() * 2 + 0.5,
                    opacity: Math.random() * 0.3 + 0.1,
                    hue: Math.random() * 60 + 180
                });
            }
            
            // 动画循环
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                particles.forEach(particle => {
                    // 更新位置
                    particle.x += particle.vx;
                    particle.y += particle.vy;
                    
                    // 边界反弹
                    if (particle.x <= 0 || particle.x >= canvas.width) particle.vx *= -1;
                    if (particle.y <= 0 || particle.y >= canvas.height) particle.vy *= -1;
                    
                    // 绘制粒子
                    ctx.save();
                    ctx.globalAlpha = particle.opacity;
                    ctx.fillStyle = `hsl(${particle.hue}, 60%, 50%)`;
                    ctx.shadowColor = `hsl(${particle.hue}, 60%, 50%)`;
                    ctx.shadowBlur = particle.size * 3;
                    
                    ctx.beginPath();
                    ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.restore();
                });
                
                requestAnimationFrame(animate);
            }
            
            animate();
            console.log('✨ 菜单背景动画已启动');
        }
        
        // 检查应用程序状态
        function checkAppState() {
            console.log('🔍 检查应用程序状态...');
            
            if (window.quantumApp) {
                console.log('📱 应用程序实例:', {
                    isInitialized: window.quantumApp.isInitialized,
                    isLoading: window.quantumApp.isLoading,
                    appState: window.quantumApp.appState
                });
            } else {
                console.log('⚠️ 应用程序实例未找到');
            }
            
            if (window.uiManager) {
                console.log('🎨 UI管理器状态:', {
                    isInitialized: window.uiManager.isInitialized,
                    currentScreen: window.uiManager.currentScreen
                });
            } else {
                console.log('⚠️ UI管理器未找到');
            }
        }
        
        // 延迟检查状态
        setTimeout(checkAppState, 1000);
        
        // 监听应用程序初始化完成事件
        document.addEventListener('DOMContentLoaded', () => {
            // 等待应用程序初始化
            const checkAndFix = () => {
                const mainMenuScreen = document.getElementById('main-menu-screen');
                const loadingScreen = document.getElementById('loading-screen');

                // 检查是否需要修复
                if (loadingScreen && loadingScreen.classList.contains('active')) {
                    console.log('⚠️ 加载屏幕仍在显示，等待应用程序初始化...');
                    setTimeout(checkAndFix, 1000);
                    return;
                }

                if (mainMenuScreen && !mainMenuScreen.classList.contains('active')) {
                    console.log('⚠️ 主菜单未自动显示，执行强制修复...');
                    window.forceShowMainMenu();
                } else if (mainMenuScreen && mainMenuScreen.classList.contains('active')) {
                    console.log('✅ 主菜单已正确显示');
                    // 确保背景动画正常工作
                    initMenuBackground();
                }
            };

            // 延迟检查，给应用程序时间初始化
            setTimeout(checkAndFix, 3000);
        });
        
        console.log('✅ 屏幕显示修复脚本初始化完成');

        // 立即检查一次
        setTimeout(() => {
            const mainMenuScreen = document.getElementById('main-menu-screen');
            const loadingScreen = document.getElementById('loading-screen');

            if (loadingScreen && loadingScreen.style.display !== 'none') {
                console.log('🔧 立即隐藏加载屏幕');
                loadingScreen.style.display = 'none';
                loadingScreen.classList.remove('active');
            }

            if (mainMenuScreen && !mainMenuScreen.classList.contains('active')) {
                console.log('🔧 立即显示主菜单');
                window.forceShowMainMenu();
            }
        }, 100);
    }
    
    // 添加全局调试函数
    window.debugScreens = function() {
        const screens = document.querySelectorAll('.screen');
        console.log('🔍 所有屏幕状态:');
        screens.forEach(screen => {
            const styles = window.getComputedStyle(screen);
            console.log(`- ${screen.id}:`, {
                display: styles.display,
                visibility: styles.visibility,
                opacity: styles.opacity,
                zIndex: styles.zIndex,
                active: screen.classList.contains('active')
            });
        });
    };
    
})();
