/**
 * 屏幕显示修复样式
 * 确保主菜单和其他屏幕能正确显示
 */

/* 强制确保屏幕基础样式 */
.screen {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: all 0.3s ease !important;
    z-index: 1 !important;
}

/* 隐藏状态 */
.screen:not(.active) {
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
}

/* 显示状态 */
.screen.active {
    opacity: 1 !important;
    visibility: visible !important;
    pointer-events: auto !important;
}

/* 加载屏幕特殊处理 */
#loading-screen {
    background: var(--bg-primary, #0d1421) !important;
    z-index: 1000 !important;
}

#loading-screen.active {
    display: flex !important;
}

#loading-screen:not(.active) {
    display: none !important;
}

/* 主菜单屏幕特殊处理 */
#main-menu-screen {
    background: linear-gradient(135deg, 
        var(--bg-primary, #0d1421) 0%, 
        var(--bg-secondary, #16213e) 100%) !important;
    z-index: 10 !important;
}

#main-menu-screen.active {
    display: flex !important;
}

/* 确保菜单容器可见 */
#main-menu-screen .menu-container {
    position: relative !important;
    z-index: 2 !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* 确保菜单按钮可见 */
#main-menu-screen .main-menu {
    display: flex !important;
    flex-direction: column !important;
    opacity: 1 !important;
    visibility: visible !important;
}

#main-menu-screen .menu-btn {
    display: flex !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* 背景画布确保在底层 */
#menu-background-canvas {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    z-index: -1 !important;
    pointer-events: none !important;
}

/* 游戏屏幕 */
#game-screen {
    background: var(--bg-primary, #0d1421) !important;
}

/* 设置屏幕 */
#settings-screen {
    background: rgba(13, 20, 33, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    z-index: 100 !important;
}

/* 暂停屏幕 */
#pause-screen {
    background: rgba(13, 20, 33, 0.9) !important;
    backdrop-filter: blur(10px) !important;
    z-index: 200 !important;
}

/* 调试辅助样式 */
.debug-visible {
    border: 2px solid red !important;
    background: rgba(255, 0, 0, 0.1) !important;
}

.debug-hidden {
    border: 2px solid blue !important;
    background: rgba(0, 0, 255, 0.1) !important;
}

/* 响应式修复 */
@media (max-width: 768px) {
    .screen {
        padding: 1rem !important;
    }
    
    #main-menu-screen .menu-container {
        max-width: 90% !important;
        padding: 1rem !important;
    }
}

/* 确保文本可见 */
#main-menu-screen * {
    color: inherit !important;
}

#main-menu-screen .main-title {
    color: var(--text-primary, #ffffff) !important;
}

#main-menu-screen .main-subtitle {
    color: var(--text-secondary, #a0a0a0) !important;
}
