/**
 * 主菜单修复验证脚本
 * 确保主菜单能正确显示并提供详细的调试信息
 */

(function() {
    'use strict';
    
    console.log('🔍 启动主菜单修复验证脚本...');
    
    // 验证配置
    const config = {
        maxRetries: 5,
        retryInterval: 1000,
        forceFixDelay: 2000
    };
    
    let retryCount = 0;
    
    // 等待DOM加载
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', startVerification);
    } else {
        startVerification();
    }
    
    function startVerification() {
        console.log('🚀 开始主菜单显示验证...');
        
        // 立即检查一次
        setTimeout(checkMainMenuStatus, 100);
        
        // 定期检查
        const checkInterval = setInterval(() => {
            if (retryCount >= config.maxRetries) {
                clearInterval(checkInterval);
                console.log('⚠️ 达到最大重试次数，停止检查');
                return;
            }
            
            checkMainMenuStatus();
            retryCount++;
        }, config.retryInterval);
        
        // 强制修复
        setTimeout(() => {
            if (!isMainMenuVisible()) {
                console.log('🔧 执行强制修复...');
                forceShowMainMenu();
            }
        }, config.forceFixDelay);
    }
    
    function checkMainMenuStatus() {
        console.log(`🔍 检查主菜单状态 (第${retryCount + 1}次)...`);
        
        const loadingScreen = document.getElementById('loading-screen');
        const mainMenuScreen = document.getElementById('main-menu-screen');
        
        // 检查加载屏幕
        if (loadingScreen) {
            const loadingVisible = isElementVisible(loadingScreen);
            console.log('📱 加载屏幕状态:', {
                exists: true,
                visible: loadingVisible,
                hasActiveClass: loadingScreen.classList.contains('active'),
                display: getComputedStyle(loadingScreen).display,
                opacity: getComputedStyle(loadingScreen).opacity
            });
            
            if (loadingVisible) {
                console.log('⚠️ 加载屏幕仍然可见，隐藏它...');
                hideLoadingScreen(loadingScreen);
            }
        }
        
        // 检查主菜单
        if (mainMenuScreen) {
            const menuVisible = isElementVisible(mainMenuScreen);
            console.log('🏠 主菜单状态:', {
                exists: true,
                visible: menuVisible,
                hasActiveClass: mainMenuScreen.classList.contains('active'),
                display: getComputedStyle(mainMenuScreen).display,
                opacity: getComputedStyle(mainMenuScreen).opacity,
                zIndex: getComputedStyle(mainMenuScreen).zIndex
            });
            
            if (!menuVisible) {
                console.log('⚠️ 主菜单不可见，尝试修复...');
                showMainMenu(mainMenuScreen);
            } else {
                console.log('✅ 主菜单正常显示');
                initializeMenuBackground();
                return true;
            }
        } else {
            console.error('❌ 主菜单元素不存在');
        }
        
        return false;
    }
    
    function isElementVisible(element) {
        if (!element) return false;
        
        const styles = getComputedStyle(element);
        return (
            styles.display !== 'none' &&
            styles.visibility !== 'hidden' &&
            parseFloat(styles.opacity) > 0 &&
            element.offsetWidth > 0 &&
            element.offsetHeight > 0
        );
    }
    
    function isMainMenuVisible() {
        const mainMenuScreen = document.getElementById('main-menu-screen');
        return mainMenuScreen && isElementVisible(mainMenuScreen);
    }
    
    function hideLoadingScreen(loadingScreen) {
        loadingScreen.classList.remove('active');
        loadingScreen.style.display = 'none';
        loadingScreen.style.visibility = 'hidden';
        loadingScreen.style.opacity = '0';
        console.log('✅ 加载屏幕已隐藏');
    }
    
    function showMainMenu(mainMenuScreen) {
        // 隐藏所有其他屏幕
        const allScreens = document.querySelectorAll('.screen');
        allScreens.forEach(screen => {
            if (screen !== mainMenuScreen) {
                screen.classList.remove('active');
                screen.style.visibility = 'hidden';
                screen.style.opacity = '0';
            }
        });
        
        // 显示主菜单
        mainMenuScreen.classList.add('active');
        mainMenuScreen.style.display = 'flex';
        mainMenuScreen.style.visibility = 'visible';
        mainMenuScreen.style.opacity = '1';
        mainMenuScreen.style.zIndex = '10';
        
        console.log('✅ 主菜单已强制显示');
        
        // 初始化背景
        setTimeout(initializeMenuBackground, 100);
    }
    
    function forceShowMainMenu() {
        console.log('🔧 执行强制主菜单显示...');
        
        const mainMenuScreen = document.getElementById('main-menu-screen');
        if (mainMenuScreen) {
            showMainMenu(mainMenuScreen);
        }
        
        // 确保菜单按钮可点击
        setTimeout(enableMenuButtons, 200);
    }
    
    function enableMenuButtons() {
        const menuButtons = document.querySelectorAll('#main-menu-screen .menu-btn');
        menuButtons.forEach(button => {
            button.style.pointerEvents = 'auto';
            button.style.opacity = '1';
        });
        console.log(`✅ 已启用 ${menuButtons.length} 个菜单按钮`);
    }
    
    function initializeMenuBackground() {
        const canvas = document.getElementById('menu-background-canvas');
        if (!canvas || canvas.dataset.initialized) return;
        
        console.log('🎨 初始化菜单背景...');
        
        const ctx = canvas.getContext('2d');
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
        canvas.dataset.initialized = 'true';
        
        // 简单的粒子背景
        const particles = [];
        for (let i = 0; i < 20; i++) {
            particles.push({
                x: Math.random() * canvas.width,
                y: Math.random() * canvas.height,
                vx: (Math.random() - 0.5) * 0.2,
                vy: (Math.random() - 0.5) * 0.2,
                size: Math.random() * 1.5 + 0.5,
                opacity: Math.random() * 0.3 + 0.1
            });
        }
        
        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            particles.forEach(particle => {
                particle.x += particle.vx;
                particle.y += particle.vy;
                
                if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
                if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;
                
                ctx.save();
                ctx.globalAlpha = particle.opacity;
                ctx.fillStyle = '#4a9eff';
                ctx.shadowColor = '#4a9eff';
                ctx.shadowBlur = particle.size * 2;
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            });
            
            requestAnimationFrame(animate);
        }
        
        animate();
        console.log('✨ 菜单背景动画已启动');
    }
    
    // 全局调试函数
    window.debugMainMenu = function() {
        console.log('🔍 主菜单调试信息:');
        checkMainMenuStatus();
    };
    
    window.forceFixMainMenu = function() {
        console.log('🔧 强制修复主菜单...');
        forceShowMainMenu();
    };
    
    console.log('✅ 主菜单修复验证脚本已加载');
    
})();
